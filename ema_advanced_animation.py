from manim import *
import numpy as np

class EMARecursiveFormula(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 递归公式展开", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 基本递归公式
        basic_formula = MathTex(
            r"\text{EMA}_t = \alpha \cdot P_t + (1-\alpha) \cdot \text{EMA}_{t-1}",
            font_size=36,
            color=WHITE
        )
        
        self.play(Write(basic_formula))
        self.wait(2)
        
        # 展开第一层
        self.play(basic_formula.animate.shift(UP * 2.5))
        
        expand1 = MathTex(
            r"\text{EMA}_t = \alpha P_t + (1-\alpha)[\alpha P_{t-1} + (1-\alpha)\text{EMA}_{t-2}]",
            font_size=28,
            color=YELLOW
        ).shift(UP * 1.5)
        
        self.play(Write(expand1))
        self.wait(2)
        
        # 展开第二层
        expand2 = MathTex(
            r"\text{EMA}_t = \alpha P_t + \alpha(1-\alpha)P_{t-1} + (1-\alpha)^2\text{EMA}_{t-2}",
            font_size=26,
            color=GREEN
        ).shift(UP * 0.5)
        
        self.play(Write(expand2))
        self.wait(2)
        
        # 完全展开形式
        expand_full = MathTex(
            r"\text{EMA}_t = \alpha \sum_{i=0}^{\infty} (1-\alpha)^i P_{t-i}",
            font_size=32,
            color=RED
        ).shift(DOWN * 0.5)
        
        self.play(Write(expand_full))
        self.wait(2)
        
        # 权重解释
        weight_explanation = Text(
            "每个历史价格的权重为 α(1-α)^i，呈指数递减",
            font_size=24,
            color=PURPLE
        ).shift(DOWN * 1.5)
        
        self.play(Write(weight_explanation))
        self.wait(3)

class EMAConvergence(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 收敛性分析", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 权重和公式
        weight_sum = MathTex(
            r"\sum_{i=0}^{\infty} \alpha(1-\alpha)^i = \alpha \cdot \frac{1}{1-(1-\alpha)} = \alpha \cdot \frac{1}{\alpha} = 1",
            font_size=32,
            color=WHITE
        )
        
        self.play(Write(weight_sum))
        self.wait(2)
        
        # 移动公式
        self.play(weight_sum.animate.shift(UP * 1.5))
        
        # 收敛性说明
        convergence_text = VGroup(
            Text("权重和为1，满足归一化条件", font_size=28, color=GREEN),
            Text("权重随时间指数衰减", font_size=28, color=YELLOW),
            Text("理论上需要无限历史数据", font_size=28, color=RED),
            Text("实际中，远期权重可忽略", font_size=28, color=PURPLE)
        ).arrange(DOWN, buff=0.5).shift(DOWN * 0.5)
        
        for text in convergence_text:
            self.play(Write(text), run_time=1)
            self.wait(0.8)
        
        self.wait(2)

class EMAInitialization(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 初始化方法", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 方法1：使用第一个价格
        method1_title = Text("方法1：使用第一个价格", font_size=32, color=YELLOW)
        method1_title.shift(UP * 2)
        
        method1_formula = MathTex(
            r"\text{EMA}_1 = P_1",
            font_size=28,
            color=WHITE
        ).shift(UP * 1.5)
        
        self.play(Write(method1_title))
        self.play(Write(method1_formula))
        self.wait(1)
        
        # 方法2：使用SMA
        method2_title = Text("方法2：使用简单移动平均", font_size=32, color=GREEN)
        method2_title.shift(UP * 0.5)
        
        method2_formula = MathTex(
            r"\text{EMA}_N = \frac{1}{N}\sum_{i=1}^{N} P_i",
            font_size=28,
            color=WHITE
        ).shift(UP * 0)
        
        self.play(Write(method2_title))
        self.play(Write(method2_formula))
        self.wait(1)
        
        # 方法3：加权初始化
        method3_title = Text("方法3：加权初始化（推荐）", font_size=32, color=RED)
        method3_title.shift(DOWN * 0.5)
        
        method3_formula = MathTex(
            r"\text{EMA}_N = \frac{\sum_{i=1}^{N} (1-\alpha)^{N-i} P_i}{\sum_{i=1}^{N} (1-\alpha)^{N-i}}",
            font_size=24,
            color=WHITE
        ).shift(DOWN * 1)
        
        self.play(Write(method3_title))
        self.play(Write(method3_formula))
        self.wait(2)
        
        # 比较说明
        comparison = Text(
            "方法3最准确，但计算复杂；方法2是常用的折中方案",
            font_size=24,
            color=PURPLE
        ).shift(DOWN * 2)
        
        self.play(Write(comparison))
        self.wait(3)

class EMAParameterSensitivity(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 参数敏感性分析", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建坐标轴
        axes = Axes(
            x_range=[0, 50, 10],
            y_range=[0, 1, 0.2],
            x_length=10,
            y_length=6,
            axis_config={"color": WHITE},
        )
        
        x_label = axes.get_x_axis_label("期数")
        y_label = axes.get_y_axis_label("权重", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 不同α值的权重衰减曲线
        alphas = [0.1, 0.2, 0.3, 0.5]
        colors = [RED, GREEN, BLUE, YELLOW]
        
        curves = VGroup()
        labels = VGroup()
        
        for alpha, color in zip(alphas, colors):
            # 计算权重序列
            x_vals = np.arange(0, 50)
            weights = [alpha * (1 - alpha) ** i for i in x_vals]
            
            # 创建曲线
            points = [axes.coords_to_point(i, weights[i]) for i in range(len(x_vals))]
            curve = VMobject()
            curve.set_points_as_corners(points)
            curve.set_color(color)
            curves.add(curve)
            
            # 创建标签
            label = MathTex(f"\\alpha = {alpha}", color=color, font_size=24)
            label.move_to(axes.coords_to_point(40, weights[40] + 0.05))
            labels.add(label)
        
        # 动画显示曲线
        for curve, label in zip(curves, labels):
            self.play(Create(curve), Write(label), run_time=1.5)
            self.wait(0.5)
        
        # 分析说明
        analysis = VGroup(
            Text("α 越大：", font_size=24, color=WHITE),
            Text("• 对近期价格更敏感", font_size=20, color=GREEN),
            Text("• 权重衰减更快", font_size=20, color=GREEN),
            Text("• 反应速度更快", font_size=20, color=GREEN),
            Text("α 越小：", font_size=24, color=WHITE),
            Text("• 更平滑，噪声更少", font_size=20, color=YELLOW),
            Text("• 权重衰减更慢", font_size=20, color=YELLOW),
            Text("• 滞后性更大", font_size=20, color=YELLOW)
        ).arrange(DOWN, aligned_edge=LEFT).to_corner(UR)
        
        self.play(Write(analysis))
        self.wait(3)

class EMAApplications(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 实际应用", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 应用场景
        applications = VGroup(
            # 技术分析
            VGroup(
                Text("📈 技术分析", font_size=32, color=YELLOW, weight=BOLD),
                Text("• 趋势识别", font_size=24, color=WHITE),
                Text("• 支撑阻力位", font_size=24, color=WHITE),
                Text("• 交易信号生成", font_size=24, color=WHITE)
            ).arrange(DOWN, aligned_edge=LEFT),
            
            # 量化策略
            VGroup(
                Text("🤖 量化策略", font_size=32, color=GREEN, weight=BOLD),
                Text("• 动量策略", font_size=24, color=WHITE),
                Text("• 均值回归", font_size=24, color=WHITE),
                Text("• 风险管理", font_size=24, color=WHITE)
            ).arrange(DOWN, aligned_edge=LEFT),
            
            # 其他领域
            VGroup(
                Text("🔬 其他领域", font_size=32, color=RED, weight=BOLD),
                Text("• 信号处理", font_size=24, color=WHITE),
                Text("• 数据平滑", font_size=24, color=WHITE),
                Text("• 预测建模", font_size=24, color=WHITE)
            ).arrange(DOWN, aligned_edge=LEFT)
        ).arrange(DOWN, buff=1, aligned_edge=LEFT).shift(LEFT * 2)
        
        # 逐个显示应用
        for app_group in applications:
            self.play(Write(app_group), run_time=2)
            self.wait(1)
        
        # 常用EMA组合
        combinations_title = Text("常用EMA组合", font_size=32, color=PURPLE, weight=BOLD)
        combinations_title.to_corner(UR).shift(UP * 2)
        
        combinations = VGroup(
            Text("• EMA(12) & EMA(26)", font_size=24, color=WHITE),
            Text("• EMA(5) & EMA(20)", font_size=24, color=WHITE),
            Text("• EMA(50) & EMA(200)", font_size=24, color=WHITE),
            Text("• 三重EMA系统", font_size=24, color=WHITE)
        ).arrange(DOWN, aligned_edge=LEFT).next_to(combinations_title, DOWN, buff=0.5)
        
        self.play(Write(combinations_title))
        for combo in combinations:
            self.play(Write(combo), run_time=0.8)
            self.wait(0.3)
        
        self.wait(3)

# 运行说明
"""
运行这些EMA高级动画的命令：

1. EMA递归公式展开：
   manim -pql ema_advanced_animation.py EMARecursiveFormula

2. EMA收敛性分析：
   manim -pql ema_advanced_animation.py EMAConvergence

3. EMA初始化方法：
   manim -pql ema_advanced_animation.py EMAInitialization

4. EMA参数敏感性：
   manim -pql ema_advanced_animation.py EMAParameterSensitivity

5. EMA实际应用：
   manim -pql ema_advanced_animation.py EMAApplications

生成完整的EMA教程视频序列：
manim -pql ema_formula_animation.py EMAIntroduction EMAFormulaExplanation AlphaCalculation
manim -pql ema_formula_animation.py EMACalculationDemo EMAvsMAComparison EMAWeightVisualization
manim -pql ema_advanced_animation.py EMARecursiveFormula EMAConvergence EMAInitialization
manim -pql ema_advanced_animation.py EMAParameterSensitivity EMAApplications
"""
