<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantStats.stats 功能地图</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3498db, #2c3e50);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
        }

        .category-section {
            margin-bottom: 50px;
        }

        .category-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            display: flex;
            align-items: center;
        }

        .category-icon {
            width: 30px;
            height: 30px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .function-card {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            background: #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
        }

        .function-name {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 1.3em;
            font-weight: bold;
        }

        .function-description {
            font-size: 0.95em;
            line-height: 1.5;
            color: #34495e;
            margin-bottom: 15px;
        }

        .function-params {
            font-size: 0.85em;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .function-returns {
            font-size: 0.9em;
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .param-details {
            font-size: 0.8em;
            color: #5d6d7e;
            background: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px;
            margin: 10px 0;
            border-radius: 0 6px 6px 0;
        }

        .param-item {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .param-name {
            font-weight: bold;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .param-type {
            color: #8e44ad;
            font-style: italic;
            font-size: 0.9em;
        }

        .param-desc {
            color: #34495e;
            margin-top: 2px;
        }

        .example-code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            margin-top: 10px;
            overflow-x: auto;
        }

        .toggle-details {
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-top: 10px;
            transition: background 0.3s ease;
        }

        .toggle-details:hover {
            background: #2980b9;
        }

        .details-content {
            display: none;
            margin-top: 10px;
        }

        .details-content.show {
            display: block;
        }

        /* 不同类别的颜色主题 */
        .basic-stats { border-color: #e74c3c; }
        .basic-stats::before { background: linear-gradient(90deg, #e74c3c, #c0392b); }
        .basic-stats .category-icon { background: #e74c3c; }

        .risk-metrics { border-color: #f39c12; }
        .risk-metrics::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
        .risk-metrics .category-icon { background: #f39c12; }

        .performance-ratios { border-color: #27ae60; }
        .performance-ratios::before { background: linear-gradient(90deg, #27ae60, #229954); }
        .performance-ratios .category-icon { background: #27ae60; }

        .drawdown-analysis { border-color: #8e44ad; }
        .drawdown-analysis::before { background: linear-gradient(90deg, #8e44ad, #7d3c98); }
        .drawdown-analysis .category-icon { background: #8e44ad; }

        .benchmark-comparison { border-color: #2980b9; }
        .benchmark-comparison::before { background: linear-gradient(90deg, #2980b9, #2471a3); }
        .benchmark-comparison .category-icon { background: #2980b9; }

        .advanced-metrics { border-color: #34495e; }
        .advanced-metrics::before { background: linear-gradient(90deg, #34495e, #2c3e50); }
        .advanced-metrics .category-icon { background: #34495e; }

        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-top: 50px;
            text-align: center;
        }

        .summary h2 {
            margin-top: 0;
            font-size: 2em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .functions-grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QuantStats.stats 功能地图</h1>
        <p class="subtitle">量化金融分析的完整工具箱 - 70+个专业指标函数</p>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon basic-stats">📊</div>
                基础统计指标 (Basic Statistics)
            </h2>
            <div class="functions-grid">
                <div class="function-card basic-stats">
                    <h3 class="function-name">compsum()</h3>
                    <div class="function-description">计算滚动复合收益率（累积乘积）- 将收益率序列转换为累积财富指数</div>
                    <div class="function-params">compsum(returns)</div>
                    <div class="function-returns">返回: pd.Series - 累积复合收益率序列</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span> <span class="param-type">(pd.Series)</span>
                                <div class="param-desc">• 收益率序列，通常是日收益率数据<br>• 格式: [0.01, -0.02, 0.03, ...] 表示1%, -2%, 3%</div>
                            </div>
                        </div>
                        <div class="example-code">
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
# 结果: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]</div>
                    </div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">comp()</h3>
                    <div class="function-description">计算总复合收益率（最终累积收益）</div>
                    <div class="function-params">参数: returns (收益率序列)</div>
                    <div class="function-returns">返回: 总复合收益率 (float)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">expected_return()</h3>
                    <div class="function-description">计算期望收益率（几何平均数）</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 期望收益率 (float)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">best()</h3>
                    <div class="function-description">找出指定期间的最佳（最高）收益率</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 最佳收益率 (float)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">worst()</h3>
                    <div class="function-description">找出指定期间的最差（最低）收益率</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 最差收益率 (float)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">avg_return()</h3>
                    <div class="function-description">计算平均收益率（排除零收益）</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 平均收益率 (float)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">win_rate()</h3>
                    <div class="function-description">计算胜率（盈利期间百分比）</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 胜率 (0-1)</div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">consecutive_wins()</h3>
                    <div class="function-description">计算最大连续盈利期间数</div>
                    <div class="function-params">参数: returns, aggregate, compounded, prepare_returns</div>
                    <div class="function-returns">返回: 最大连续盈利次数 (int)</div>
                </div>
            </div>
        </div>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon risk-metrics">⚠️</div>
                风险指标 (Risk Metrics)
            </h2>
            <div class="functions-grid">
                <div class="function-card risk-metrics">
                    <h3 class="function-name">volatility()</h3>
                    <div class="function-description">计算收益率的波动率（标准差）</div>
                    <div class="function-params">参数: returns, periods=252, annualize=True</div>
                    <div class="function-returns">返回: 波动率 (float)</div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">value_at_risk()</h3>
                    <div class="function-description">计算日度风险价值（VaR）</div>
                    <div class="function-params">参数: returns, sigma=1, confidence=0.95</div>
                    <div class="function-returns">返回: VaR值 (float)</div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">conditional_value_at_risk()</h3>
                    <div class="function-description">计算条件风险价值（CVaR/期望损失）</div>
                    <div class="function-params">参数: returns, sigma=1, confidence=0.95</div>
                    <div class="function-returns">返回: CVaR值 (float)</div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">skew()</h3>
                    <div class="function-description">计算收益率分布的偏度</div>
                    <div class="function-params">参数: returns, prepare_returns=True</div>
                    <div class="function-returns">返回: 偏度值 (float)</div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">kurtosis()</h3>
                    <div class="function-description">计算收益率分布的峰度</div>
                    <div class="function-params">参数: returns, prepare_returns=True</div>
                    <div class="function-returns">返回: 峰度值 (float)</div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">distribution()</h3>
                    <div class="function-description">分析不同时间段的收益分布</div>
                    <div class="function-params">参数: returns, compounded=True, prepare_returns=True</div>
                    <div class="function-returns">返回: 各时间段分布字典</div>
                </div>
            </div>
        </div>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon performance-ratios">📈</div>
                绩效比率 (Performance Ratios)
            </h2>
            <div class="functions-grid">
                <div class="function-card performance-ratios">
                    <h3 class="function-name">sharpe()</h3>
                    <div class="function-description">计算夏普比率（风险调整后收益）- 衡量每单位风险的超额收益</div>
                    <div class="function-params">sharpe(returns, rf=0.0, periods=252, annualize=True, smart=False)</div>
                    <div class="function-returns">返回: float - 夏普比率值</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span> <span class="param-type">(pd.Series)</span>
                                <div class="param-desc">• 投资组合的历史收益率序列<br>• 通常是日收益率: [0.01, -0.02, 0.03, ...]</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">rf</span> <span class="param-type">(float, 默认=0.0)</span>
                                <div class="param-desc">• 无风险利率（年化）<br>• 0.03 表示3%的年化无风险利率<br>• 用于计算超额收益</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">periods</span> <span class="param-type">(int, 默认=252)</span>
                                <div class="param-desc">• 年化周期数<br>• 252: 股票交易日 | 365: 日历天数<br>• 12: 月度数据 | 4: 季度数据</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">annualize</span> <span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">• 是否年化结果<br>• True: 年化夏普比率 | False: 原始周期夏普比率</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">smart</span> <span class="param-type">(bool, 默认=False)</span>
                                <div class="param-desc">• 是否应用自相关惩罚<br>• True: 考虑收益率自相关性，更准确的风险调整</div>
                            </div>
                        </div>
                        <div class="example-code">
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
sharpe_basic = qs.stats.sharpe(returns)                    # 基础夏普比率
sharpe_with_rf = qs.stats.sharpe(returns, rf=0.03)         # 考虑3%无风险利率
sharpe_smart = qs.stats.sharpe(returns, smart=True)        # 智能模式</div>
                    </div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">sortino()</h3>
                    <div class="function-description">计算索提诺比率（下行风险调整收益）</div>
                    <div class="function-params">参数: returns, rf=0, periods=252, annualize=True</div>
                    <div class="function-returns">返回: 索提诺比率 (float)</div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">calmar()</h3>
                    <div class="function-description">计算卡玛比率（年化收益/最大回撤）</div>
                    <div class="function-params">参数: returns, prepare_returns=True</div>
                    <div class="function-returns">返回: 卡玛比率 (float)</div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">cagr()</h3>
                    <div class="function-description">计算复合年增长率</div>
                    <div class="function-params">参数: returns, rf=0, compounded=True</div>
                    <div class="function-returns">返回: CAGR (float)</div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">smart_sharpe()</h3>
                    <div class="function-description">计算智能夏普比率（含自相关惩罚）</div>
                    <div class="function-params">参数: returns, rf=0.0, periods=252, annualize=True</div>
                    <div class="function-returns">返回: 智能夏普比率 (float)</div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">rolling_sharpe()</h3>
                    <div class="function-description">计算滚动夏普比率</div>
                    <div class="function-params">参数: returns, rf=0.0, rolling_period=126</div>
                    <div class="function-returns">返回: 滚动夏普比率序列</div>
                </div>
            </div>
        </div>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon drawdown-analysis">📉</div>
                回撤分析 (Drawdown Analysis)
            </h2>
            <div class="functions-grid">
                <div class="function-card drawdown-analysis">
                    <h3 class="function-name">max_drawdown()</h3>
                    <div class="function-description">计算最大回撤（从峰值到谷底）</div>
                    <div class="function-params">参数: prices (价格序列或累积收益)</div>
                    <div class="function-returns">返回: 最大回撤 (负值)</div>
                </div>

                <div class="function-card drawdown-analysis">
                    <h3 class="function-name">to_drawdown_series()</h3>
                    <div class="function-description">将收益序列转换为回撤序列</div>
                    <div class="function-params">参数: returns (收益率序列)</div>
                    <div class="function-returns">返回: 回撤序列</div>
                </div>

                <div class="function-card drawdown-analysis">
                    <h3 class="function-name">ulcer_index()</h3>
                    <div class="function-description">计算溃疡指数（回撤的均方根）</div>
                    <div class="function-params">参数: returns, prepare_returns=True</div>
                    <div class="function-returns">返回: 溃疡指数 (float)</div>
                </div>

                <div class="function-card drawdown-analysis">
                    <h3 class="function-name">drawdown_details()</h3>
                    <div class="function-description">计算每个回撤期间的详细统计</div>
                    <div class="function-params">参数: drawdown (回撤序列)</div>
                    <div class="function-returns">返回: 回撤详情DataFrame</div>
                </div>
            </div>
        </div>

        <div class="summary">
            <h2>QuantStats.stats 完整功能概览</h2>
            <p>专业量化金融分析工具箱，提供全面的投资组合分析能力</p>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">70+</div>
                    <div class="stat-label">专业指标函数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">主要功能类别</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">开源免费</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5.9k</div>
                    <div class="stat-label">GitHub Stars</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDetails(button) {
            const detailsContent = button.nextElementSibling;
            const isVisible = detailsContent.classList.contains('show');

            if (isVisible) {
                detailsContent.classList.remove('show');
                button.innerHTML = '详细参数 ▼';
            } else {
                detailsContent.classList.add('show');
                button.innerHTML = '详细参数 ▲';
            }
        }

        // 添加全局展开/收起功能
        document.addEventListener('DOMContentLoaded', function() {
            // 创建全局控制按钮
            const globalControls = document.createElement('div');
            globalControls.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 10px;
            `;

            const expandAllBtn = document.createElement('button');
            expandAllBtn.textContent = '展开全部';
            expandAllBtn.style.cssText = `
                background: #27ae60;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9em;
            `;

            const collapseAllBtn = document.createElement('button');
            collapseAllBtn.textContent = '收起全部';
            collapseAllBtn.style.cssText = `
                background: #e74c3c;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9em;
            `;

            expandAllBtn.onclick = function() {
                document.querySelectorAll('.details-content').forEach(content => {
                    content.classList.add('show');
                });
                document.querySelectorAll('.toggle-details').forEach(btn => {
                    btn.innerHTML = '详细参数 ▲';
                });
            };

            collapseAllBtn.onclick = function() {
                document.querySelectorAll('.details-content').forEach(content => {
                    content.classList.remove('show');
                });
                document.querySelectorAll('.toggle-details').forEach(btn => {
                    btn.innerHTML = '详细参数 ▼';
                });
            };

            globalControls.appendChild(expandAllBtn);
            globalControls.appendChild(collapseAllBtn);
            document.body.appendChild(globalControls);
        });
    </script>
</body>
</html>
