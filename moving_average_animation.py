from manim import *
import numpy as np
import pandas as pd

class MovingAverageAnimation(Scene):
    def construct(self):
        # 设置标题
        title = Text("低频延迟均线动画演示", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建坐标轴
        axes = Axes(
            x_range=[0, 50, 10],
            y_range=[90, 110, 5],
            x_length=10,
            y_length=6,
            axis_config={"color": WHITE},
            x_axis_config={
                "numbers_to_include": np.arange(0, 51, 10),
                "numbers_with_elongated_ticks": np.arange(0, 51, 10),
            },
            y_axis_config={
                "numbers_to_include": np.arange(90, 111, 5),
                "numbers_with_elongated_ticks": np.arange(90, 111, 5),
            },
            tips=False,
        )
        
        # 添加轴标签
        x_label = axes.get_x_axis_label("时间")
        y_label = axes.get_y_axis_label("价格", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 生成模拟价格数据
        np.random.seed(42)
        n_points = 50
        base_price = 100
        price_data = []
        
        for i in range(n_points):
            # 添加趋势和随机波动
            trend = 0.1 * i
            noise = np.random.normal(0, 2)
            price = base_price + trend + noise
            price_data.append(price)
        
        # 计算移动平均线
        def calculate_ma(data, window):
            ma = []
            for i in range(len(data)):
                if i < window - 1:
                    ma.append(None)
                else:
                    ma.append(np.mean(data[i-window+1:i+1]))
            return ma
        
        ma5 = calculate_ma(price_data, 5)
        ma20 = calculate_ma(price_data, 20)
        
        # 创建价格线
        price_points = [axes.coords_to_point(i, price_data[i]) for i in range(n_points)]
        price_line = VMobject()
        price_line.set_points_as_corners(price_points)
        price_line.set_color(WHITE)
        
        # 创建移动平均线
        ma5_points = [axes.coords_to_point(i, ma5[i]) for i in range(4, n_points) if ma5[i] is not None]
        ma5_line = VMobject()
        ma5_line.set_points_as_corners(ma5_points)
        ma5_line.set_color(YELLOW)
        
        ma20_points = [axes.coords_to_point(i, ma20[i]) for i in range(19, n_points) if ma20[i] is not None]
        ma20_line = VMobject()
        ma20_line.set_points_as_corners(ma20_points)
        ma20_line.set_color(RED)
        
        # 添加图例
        legend = VGroup(
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=WHITE), Text("价格", font_size=24).next_to(RIGHT * 0.6, RIGHT)),
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=YELLOW), Text("MA5", font_size=24).next_to(RIGHT * 0.6, RIGHT)),
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=RED), Text("MA20", font_size=24).next_to(RIGHT * 0.6, RIGHT))
        ).arrange(DOWN, aligned_edge=LEFT).to_corner(UR)
        
        # 动画绘制
        self.play(Create(price_line), run_time=3)
        self.wait(1)
        
        self.play(Create(ma5_line), run_time=2)
        self.wait(1)
        
        self.play(Create(ma20_line), run_time=2)
        self.wait(1)
        
        self.play(Write(legend))
        self.wait(2)
        
        # 添加解释文本
        explanation = Text(
            "移动平均线平滑价格波动\n短期均线反应更快\n长期均线更加稳定",
            font_size=32,
            color=GREEN
        ).to_corner(DL)
        
        self.play(Write(explanation))
        self.wait(3)

class QuantStatsDemo(Scene):
    def construct(self):
        # 标题
        title = Text("QuantStats 指标演示", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建指标卡片
        def create_metric_card(name, formula, description, color):
            card = Rectangle(width=6, height=3, color=color, fill_opacity=0.1)
            name_text = Text(name, font_size=32, color=color).move_to(card.get_top() + DOWN * 0.5)
            formula_text = MathTex(formula, font_size=24).move_to(card.get_center())
            desc_text = Text(description, font_size=20).move_to(card.get_bottom() + UP * 0.5)
            
            return VGroup(card, name_text, formula_text, desc_text)
        
        # 创建三个指标卡片
        expected_return_card = create_metric_card(
            "Expected Return",
            r"\left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}} - 1",
            "几何平均收益率",
            YELLOW
        )
        
        sharpe_card = create_metric_card(
            "Sharpe Ratio",
            r"\frac{E[R] - R_f}{\sigma}",
            "风险调整收益",
            GREEN
        )
        
        max_dd_card = create_metric_card(
            "Max Drawdown",
            r"\max_{t \in [0,T]} \left[ \frac{P_t - \max_{s \in [0,t]} P_s}{\max_{s \in [0,t]} P_s} \right]",
            "最大回撤",
            RED
        )
        
        # 排列卡片
        cards = VGroup(expected_return_card, sharpe_card, max_dd_card)
        cards.arrange(RIGHT, buff=0.5).move_to(ORIGIN)
        
        # 动画显示卡片
        for card in cards:
            self.play(FadeIn(card), run_time=1.5)
            self.wait(0.5)
        
        self.wait(2)
        
        # 添加解释
        explanation = Text(
            "这些是量化投资中的核心指标\n用于评估投资策略的表现",
            font_size=28,
            color=WHITE
        ).to_edge(DOWN)
        
        self.play(Write(explanation))
        self.wait(3)

class FormulaExplanation(Scene):
    def construct(self):
        # 标题
        title = Text("几何平均收益率公式详解", font_size=40, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 完整公式
        full_formula = MathTex(
            r"\text{Expected Return} = \left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}} - 1",
            font_size=36
        )
        self.play(Write(full_formula))
        self.wait(2)
        
        # 分步解释
        step1 = MathTex(r"\text{步骤1: } 1 + r_i", font_size=32, color=YELLOW)
        step1_desc = Text("转换为价格乘数", font_size=24).next_to(step1, DOWN)
        
        step2 = MathTex(r"\text{步骤2: } \prod_{i=1}^{n}(1 + r_i)", font_size=32, color=GREEN)
        step2_desc = Text("计算累积乘积", font_size=24).next_to(step2, DOWN)
        
        step3 = MathTex(r"\text{步骤3: } \left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}}", font_size=32, color=RED)
        step3_desc = Text("开n次方", font_size=24).next_to(step3, DOWN)
        
        step4 = MathTex(r"\text{步骤4: } \left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}} - 1", font_size=32, color=PURPLE)
        step4_desc = Text("减1得到收益率", font_size=24).next_to(step4, DOWN)
        
        steps = VGroup(
            VGroup(step1, step1_desc),
            VGroup(step2, step2_desc),
            VGroup(step3, step3_desc),
            VGroup(step4, step4_desc)
        ).arrange(DOWN, buff=0.8).move_to(ORIGIN + DOWN * 0.5)
        
        # 逐步显示
        self.play(full_formula.animate.to_edge(UP, buff=1))
        
        for step in steps:
            self.play(FadeIn(step), run_time=1.5)
            self.wait(1)
        
        self.wait(3)

# 运行动画的命令示例
if __name__ == "__main__":
    # 在命令行中运行以下命令来生成视频：
    # manim -pql moving_average_animation.py MovingAverageAnimation
    # manim -pql moving_average_animation.py QuantStatsDemo  
    # manim -pql moving_average_animation.py FormulaExplanation
    pass
