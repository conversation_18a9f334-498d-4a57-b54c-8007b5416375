from manim import *
import numpy as np

class QuantStatsIntro(Scene):
    def construct(self):
        # 主标题
        main_title = Text("QuantStats", font_size=72, color=BLUE, weight=BOLD)
        subtitle = Text("Python量化分析工具包", font_size=36, color=WHITE)
        
        title_group = VGroup(main_title, subtitle).arrange(DOWN, buff=0.5)
        
        self.play(Write(main_title), run_time=2)
        self.play(Write(subtitle), run_time=1.5)
        self.wait(1)
        
        # 移动标题
        self.play(title_group.animate.to_edge(UP, buff=0.5))
        
        # 功能特点
        features = [
            "📊 70+ 专业量化指标",
            "📈 风险调整收益分析", 
            "📉 回撤与波动率计算",
            "🎯 投资组合绩效评估",
            "📋 自动化报告生成"
        ]
        
        feature_objects = []
        for i, feature in enumerate(features):
            feature_text = Text(feature, font_size=32, color=YELLOW)
            feature_text.shift(DOWN * (i * 0.8 - 1))
            feature_objects.append(feature_text)
        
        # 逐个显示特性
        for feature in feature_objects:
            self.play(Write(feature), run_time=1)
            self.wait(0.5)
        
        self.wait(2)

class ExpectedReturnFormula(Scene):
    def construct(self):
        # 标题
        title = Text("Expected Return 公式详解", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 完整公式
        formula = MathTex(
            r"\text{Expected Return} = \left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}} - 1",
            font_size=40,
            color=WHITE
        )
        
        self.play(Write(formula))
        self.wait(2)
        
        # 移动公式到上方
        self.play(formula.animate.shift(UP * 1.5))
        
        # 步骤解释
        steps = [
            ("步骤1", r"1 + r_i", "转换为价格乘数", YELLOW),
            ("步骤2", r"\prod_{i=1}^{n}(1 + r_i)", "计算累积乘积", GREEN),
            ("步骤3", r"\left(\prod_{i=1}^{n}(1 + r_i)\right)^{\frac{1}{n}}", "开n次方", RED),
            ("步骤4", r"\text{结果} - 1", "减1得到收益率", PURPLE)
        ]
        
        step_group = VGroup()
        for i, (step_name, step_formula, step_desc, color) in enumerate(steps):
            step_text = Text(step_name, font_size=24, color=color)
            step_math = MathTex(step_formula, font_size=28, color=color)
            step_description = Text(step_desc, font_size=20, color=WHITE)
            
            step_item = VGroup(step_text, step_math, step_description).arrange(RIGHT, buff=0.5)
            step_item.shift(DOWN * (i * 0.8 - 0.5))
            step_group.add(step_item)
        
        # 逐步显示
        for step in step_group:
            self.play(FadeIn(step), run_time=1.2)
            self.wait(0.8)
        
        self.wait(2)

class RiskMetricsComparison(Scene):
    def construct(self):
        # 标题
        title = Text("风险指标对比", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建三个指标卡片
        def create_metric_card(name, formula, description, color, position):
            # 卡片背景
            card = RoundedRectangle(
                width=3.5, 
                height=4, 
                corner_radius=0.2,
                color=color,
                fill_opacity=0.1,
                stroke_width=2
            )
            
            # 指标名称
            name_text = Text(name, font_size=28, color=color, weight=BOLD)
            name_text.move_to(card.get_top() + DOWN * 0.5)
            
            # 公式
            formula_text = MathTex(formula, font_size=20, color=WHITE)
            formula_text.move_to(card.get_center())
            
            # 描述
            desc_text = Text(description, font_size=18, color=WHITE)
            desc_text.move_to(card.get_bottom() + UP * 0.8)
            
            # 组合
            card_group = VGroup(card, name_text, formula_text, desc_text)
            card_group.move_to(position)
            
            return card_group
        
        # 创建三个指标
        sharpe_card = create_metric_card(
            "Sharpe Ratio",
            r"\frac{E[R] - R_f}{\sigma}",
            "风险调整收益\n值越高越好",
            YELLOW,
            LEFT * 4
        )
        
        sortino_card = create_metric_card(
            "Sortino Ratio", 
            r"\frac{E[R] - R_f}{\sigma_{down}}",
            "下行风险调整\n只考虑负收益",
            GREEN,
            ORIGIN
        )
        
        calmar_card = create_metric_card(
            "Calmar Ratio",
            r"\frac{\text{CAGR}}{|\text{Max DD}|}",
            "回撤调整收益\n年化收益/最大回撤",
            RED,
            RIGHT * 4
        )
        
        # 动画显示卡片
        cards = [sharpe_card, sortino_card, calmar_card]
        for i, card in enumerate(cards):
            self.play(FadeIn(card), run_time=1.5)
            self.wait(0.5)
        
        # 添加对比说明
        comparison_text = Text(
            "三个指标从不同角度衡量风险调整后的收益",
            font_size=24,
            color=WHITE
        )
        comparison_text.to_edge(DOWN, buff=1)
        
        self.play(Write(comparison_text))
        self.wait(3)

class DataVisualization(Scene):
    def construct(self):
        # 标题
        title = Text("收益率可视化", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建坐标轴
        axes = Axes(
            x_range=[0, 12, 2],
            y_range=[-0.1, 0.3, 0.1],
            x_length=10,
            y_length=6,
            axis_config={"color": WHITE},
            x_axis_config={
                "numbers_to_include": np.arange(0, 13, 2),
            },
            y_axis_config={
                "numbers_to_include": np.arange(-0.1, 0.31, 0.1),
                "decimal_number_config": {"num_decimal_places": 1},
            },
        )
        
        # 轴标签
        x_label = axes.get_x_axis_label("月份")
        y_label = axes.get_y_axis_label("收益率", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 模拟月度收益数据
        monthly_returns = [0.05, -0.02, 0.08, -0.03, 0.12, 0.01, -0.05, 0.09, -0.01, 0.06, 0.03, 0.04]
        
        # 创建柱状图
        bars = VGroup()
        for i, ret in enumerate(monthly_returns):
            color = GREEN if ret > 0 else RED
            bar_height = abs(ret) * 20  # 缩放因子
            
            if ret > 0:
                bar = Rectangle(width=0.6, height=bar_height, color=color, fill_opacity=0.7)
                bar.move_to(axes.coords_to_point(i+1, ret/2))
            else:
                bar = Rectangle(width=0.6, height=bar_height, color=color, fill_opacity=0.7)
                bar.move_to(axes.coords_to_point(i+1, ret/2))
            
            bars.add(bar)
        
        # 动画显示柱状图
        for bar in bars:
            self.play(GrowFromEdge(bar, DOWN), run_time=0.3)
        
        self.wait(1)
        
        # 添加统计信息
        stats_text = VGroup(
            Text(f"平均收益: {np.mean(monthly_returns):.2%}", font_size=24, color=WHITE),
            Text(f"波动率: {np.std(monthly_returns):.2%}", font_size=24, color=WHITE),
            Text(f"最大收益: {max(monthly_returns):.2%}", font_size=24, color=GREEN),
            Text(f"最大亏损: {min(monthly_returns):.2%}", font_size=24, color=RED)
        ).arrange(DOWN, aligned_edge=LEFT).to_corner(UR)
        
        self.play(Write(stats_text))
        self.wait(3)

# 运行说明
"""
运行这些动画的命令：

1. QuantStats 介绍：
   manim -pql quantstats_animation.py QuantStatsIntro

2. Expected Return 公式：
   manim -pql quantstats_animation.py ExpectedReturnFormula

3. 风险指标对比：
   manim -pql quantstats_animation.py RiskMetricsComparison

4. 数据可视化：
   manim -pql quantstats_animation.py DataVisualization

生成高质量视频：
manim -pqh quantstats_animation.py QuantStatsIntro

批量生成所有动画：
manim -pql quantstats_animation.py QuantStatsIntro ExpectedReturnFormula RiskMetricsComparison DataVisualization
"""
