<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantStats.stats 详细功能地图</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3498db, #2c3e50);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
        }

        .category-section {
            margin-bottom: 50px;
        }

        .category-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            display: flex;
            align-items: center;
        }

        .category-icon {
            width: 30px;
            height: 30px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            background: #3498db;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .function-card {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 25px;
            background: #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
        }

        .function-name {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 1.4em;
            font-weight: bold;
        }

        .function-description {
            font-size: 1em;
            line-height: 1.5;
            color: #34495e;
            margin-bottom: 15px;
        }

        .function-signature {
            font-size: 0.9em;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .function-returns {
            font-size: 0.95em;
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .param-details {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .param-item {
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .param-name {
            font-weight: bold;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            background: #e8f4f8;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .param-type {
            color: #8e44ad;
            font-style: italic;
            font-size: 0.9em;
            margin-left: 8px;
        }

        .param-desc {
            color: #34495e;
            margin-top: 5px;
            padding-left: 10px;
        }

        .example-code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            margin-top: 15px;
            overflow-x: auto;
            line-height: 1.4;
        }

        .toggle-details {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .toggle-details:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .details-content {
            display: none;
            margin-top: 15px;
        }

        .details-content.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 不同类别的颜色主题 */
        .basic-stats { border-color: #e74c3c; }
        .basic-stats::before { background: linear-gradient(90deg, #e74c3c, #c0392b); }

        .risk-metrics { border-color: #f39c12; }
        .risk-metrics::before { background: linear-gradient(90deg, #f39c12, #e67e22); }

        .performance-ratios { border-color: #27ae60; }
        .performance-ratios::before { background: linear-gradient(90deg, #27ae60, #229954); }

        .drawdown-analysis { border-color: #8e44ad; }
        .drawdown-analysis::before { background: linear-gradient(90deg, #8e44ad, #7d3c98); }

        .benchmark-comparison { border-color: #2980b9; }
        .benchmark-comparison::before { background: linear-gradient(90deg, #2980b9, #2471a3); }

        .advanced-metrics { border-color: #34495e; }
        .advanced-metrics::before { background: linear-gradient(90deg, #34495e, #2c3e50); }

        .global-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .global-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .expand-all {
            background: #27ae60;
            color: white;
        }

        .expand-all:hover {
            background: #229954;
        }

        .collapse-all {
            background: #e74c3c;
            color: white;
        }

        .collapse-all:hover {
            background: #c0392b;
        }

        @media (max-width: 768px) {
            .functions-grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .container {
                padding: 20px;
            }

            .global-controls {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="global-controls">
        <button class="global-btn expand-all" onclick="expandAll()">展开全部</button>
        <button class="global-btn collapse-all" onclick="collapseAll()">收起全部</button>
    </div>

    <div class="container">
        <h1>QuantStats.stats 详细功能地图</h1>
        <p class="subtitle">量化金融分析的完整工具箱 - 每个函数的详细参数说明</p>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon">📊</div>
                基础统计指标 (Basic Statistics)
            </h2>
            <div class="functions-grid">
                <div class="function-card basic-stats">
                    <h3 class="function-name">compsum()</h3>
                    <div class="function-description">计算滚动复合收益率（累积乘积）- 将收益率序列转换为累积财富指数</div>
                    <div class="function-signature">compsum(returns)</div>
                    <div class="function-returns">返回: pd.Series - 累积复合收益率序列</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">
                                    • 收益率序列，通常是日收益率数据<br>
                                    • 格式: [0.01, -0.02, 0.03, ...] 表示1%, -2%, 3%<br>
                                    • 计算公式: (1 + returns).cumprod() - 1
                                </div>
                            </div>
                        </div>
                        <div class="example-code">
# 示例用法
import quantstats as qs
import pandas as pd

returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
print(cumulative)
# 输出: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]

# 实际含义：
# 第1天: 1% 收益
# 第2天: (1.01 * 0.98) - 1 = -0.98%
# 第3天: (0.9902 * 1.03) - 1 = 2.07%</div>
                    </div>
                </div>

                <div class="function-card basic-stats">
                    <h3 class="function-name">comp()</h3>
                    <div class="function-description">计算总复合收益率（最终累积收益）- 整个期间的总收益率</div>
                    <div class="function-signature">comp(returns)</div>
                    <div class="function-returns">返回: float - 总复合收益率</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">
                                    • 收益率序列<br>
                                    • 计算公式: (1 + returns).prod() - 1<br>
                                    • 等同于 compsum() 的最后一个值
                                </div>
                            </div>
                        </div>
                        <div class="example-code">
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
total_return = qs.stats.comp(returns)
print(f"总收益率: {total_return:.4f}")  # 0.0307

# 等价于
cumulative = qs.stats.compsum(returns)
print(f"最后值: {cumulative.iloc[-1]:.4f}")  # 0.0307</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon">📈</div>
                绩效比率 (Performance Ratios)
            </h2>
            <div class="functions-grid">
                <div class="function-card performance-ratios">
                    <h3 class="function-name">sharpe()</h3>
                    <div class="function-description">计算夏普比率（风险调整后收益）- 衡量每单位风险的超额收益</div>
                    <div class="function-signature">sharpe(returns, rf=0.0, periods=252, annualize=True, smart=False)</div>
                    <div class="function-returns">返回: float - 夏普比率值</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">
                                    • 投资组合的历史收益率序列<br>
                                    • 通常是日收益率: [0.01, -0.02, 0.03, ...]
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">rf</span><span class="param-type">(float, 默认=0.0)</span>
                                <div class="param-desc">
                                    • 无风险利率（年化）<br>
                                    • 0.03 表示3%的年化无风险利率<br>
                                    • 用于计算超额收益 (returns - rf)
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">periods</span><span class="param-type">(int, 默认=252)</span>
                                <div class="param-desc">
                                    • 年化周期数，用于将结果年化<br>
                                    • 252: 股票交易日数 | 365: 日历天数<br>
                                    • 12: 月度数据 | 4: 季度数据
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">annualize</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">
                                    • 是否年化结果<br>
                                    • True: 年化夏普比率 × √periods<br>
                                    • False: 原始周期夏普比率
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">smart</span><span class="param-type">(bool, 默认=False)</span>
                                <div class="param-desc">
                                    • 是否应用自相关惩罚因子<br>
                                    • True: 考虑收益率自相关性，调整分母<br>
                                    • 适用于趋势跟踪等有自相关的策略
                                </div>
                            </div>
                        </div>
                        <div class="example-code">
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

# 基础夏普比率
sharpe_basic = qs.stats.sharpe(returns)
print(f"基础夏普比率: {sharpe_basic:.4f}")

# 考虑3%无风险利率
sharpe_with_rf = qs.stats.sharpe(returns, rf=0.03)
print(f"考虑无风险利率: {sharpe_with_rf:.4f}")

# 月度数据年化
sharpe_monthly = qs.stats.sharpe(returns, periods=12)
print(f"月度数据年化: {sharpe_monthly:.4f}")

# 智能模式（考虑自相关）
sharpe_smart = qs.stats.sharpe(returns, smart=True)
print(f"智能夏普比率: {sharpe_smart:.4f}")

# 计算公式: (平均收益 - 无风险利率) / 收益标准差 × √年化周期</div>
                    </div>
                <div class="function-card performance-ratios">
                    <h3 class="function-name">sortino()</h3>
                    <div class="function-description">计算索提诺比率（下行风险调整收益）- 只考虑负收益的风险</div>
                    <div class="function-signature">sortino(returns, rf=0, periods=252, annualize=True, smart=False)</div>
                    <div class="function-returns">返回: float - 索提诺比率值</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">• 收益率序列</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">rf</span><span class="param-type">(float, 默认=0)</span>
                                <div class="param-desc">• 无风险利率（年化）</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">periods</span><span class="param-type">(int, 默认=252)</span>
                                <div class="param-desc">• 年化周期数</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">annualize</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">• 是否年化结果</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">smart</span><span class="param-type">(bool, 默认=False)</span>
                                <div class="param-desc">• 是否应用自相关惩罚</div>
                            </div>
                        </div>
                        <div class="example-code">
# 索提诺比率 vs 夏普比率
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

sortino_ratio = qs.stats.sortino(returns)
sharpe_ratio = qs.stats.sharpe(returns)

print(f"索提诺比率: {sortino_ratio:.4f}")
print(f"夏普比率: {sharpe_ratio:.4f}")

# 索提诺比率通常高于夏普比率，因为只考虑下行风险
# 计算公式: (平均收益 - 无风险利率) / 下行标准差</div>
                    </div>
                </div>

                <div class="function-card performance-ratios">
                    <h3 class="function-name">calmar()</h3>
                    <div class="function-description">计算卡玛比率（年化收益/最大回撤）- 衡量回撤风险调整收益</div>
                    <div class="function-signature">calmar(returns, prepare_returns=True)</div>
                    <div class="function-returns">返回: float - 卡玛比率值</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">
                                    • 收益率序列<br>
                                    • 用于计算CAGR和最大回撤
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">prepare_returns</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">
                                    • 是否预处理收益率数据<br>
                                    • 包括去除NaN值、格式转换等
                                </div>
                            </div>
                        </div>
                        <div class="example-code">
# 卡玛比率示例
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

calmar_ratio = qs.stats.calmar(returns)
cagr = qs.stats.cagr(returns)
max_dd = qs.stats.max_drawdown(returns)

print(f"卡玛比率: {calmar_ratio:.4f}")
print(f"CAGR: {cagr:.4f}")
print(f"最大回撤: {max_dd:.4f}")

# 计算公式: CAGR / abs(最大回撤)
# 值越高表示每单位回撤风险的收益越高</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="category-section">
            <h2 class="category-title">
                <div class="category-icon">⚠️</div>
                风险指标 (Risk Metrics)
            </h2>
            <div class="functions-grid">
                <div class="function-card risk-metrics">
                    <h3 class="function-name">volatility()</h3>
                    <div class="function-description">计算收益率的波动率（标准差）- 衡量收益率的变动程度</div>
                    <div class="function-signature">volatility(returns, periods=252, annualize=True, prepare_returns=True)</div>
                    <div class="function-returns">返回: float - 波动率值</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">• 收益率序列</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">periods</span><span class="param-type">(int, 默认=252)</span>
                                <div class="param-desc">
                                    • 年化周期数<br>
                                    • 用于将日度波动率转换为年化波动率
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">annualize</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">
                                    • 是否年化波动率<br>
                                    • True: 年化波动率 = 日波动率 × √periods
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">prepare_returns</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">• 是否预处理收益率数据</div>
                            </div>
                        </div>
                        <div class="example-code">
# 波动率计算示例
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

# 年化波动率
annual_vol = qs.stats.volatility(returns, annualize=True)
print(f"年化波动率: {annual_vol:.4f}")

# 日度波动率
daily_vol = qs.stats.volatility(returns, annualize=False)
print(f"日度波动率: {daily_vol:.4f}")

# 验证年化关系
print(f"验证: {daily_vol * np.sqrt(252):.4f}")

# 波动率越高，风险越大</div>
                    </div>
                </div>

                <div class="function-card risk-metrics">
                    <h3 class="function-name">value_at_risk()</h3>
                    <div class="function-description">计算风险价值（VaR）- 在给定置信水平下的最大预期损失</div>
                    <div class="function-signature">value_at_risk(returns, sigma=1, confidence=0.95, prepare_returns=True)</div>
                    <div class="function-returns">返回: float - VaR值（负数表示损失）</div>
                    <button class="toggle-details" onclick="toggleDetails(this)">详细参数 ▼</button>
                    <div class="details-content">
                        <div class="param-details">
                            <div class="param-item">
                                <span class="param-name">returns</span><span class="param-type">(pd.Series, 必需)</span>
                                <div class="param-desc">• 收益率序列</div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">sigma</span><span class="param-type">(float, 默认=1)</span>
                                <div class="param-desc">
                                    • 波动率乘数<br>
                                    • 用于调整风险估计的敏感度
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">confidence</span><span class="param-type">(float, 默认=0.95)</span>
                                <div class="param-desc">
                                    • 置信水平<br>
                                    • 0.95 = 95%置信度<br>
                                    • 0.99 = 99%置信度
                                </div>
                            </div>
                            <div class="param-item">
                                <span class="param-name">prepare_returns</span><span class="param-type">(bool, 默认=True)</span>
                                <div class="param-desc">• 是否预处理收益率数据</div>
                            </div>
                        </div>
                        <div class="example-code">
# VaR计算示例
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

# 95% VaR
var_95 = qs.stats.value_at_risk(returns, confidence=0.95)
print(f"95% VaR: {var_95:.4f}")

# 99% VaR
var_99 = qs.stats.value_at_risk(returns, confidence=0.99)
print(f"99% VaR: {var_99:.4f}")

# 解释：95%的情况下，日损失不会超过 var_95
# 使用正态分布假设计算</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDetails(button) {
            const detailsContent = button.nextElementSibling;
            const isVisible = detailsContent.classList.contains('show');
            
            if (isVisible) {
                detailsContent.classList.remove('show');
                button.innerHTML = '详细参数 ▼';
            } else {
                detailsContent.classList.add('show');
                button.innerHTML = '详细参数 ▲';
            }
        }

        function expandAll() {
            document.querySelectorAll('.details-content').forEach(content => {
                content.classList.add('show');
            });
            document.querySelectorAll('.toggle-details').forEach(btn => {
                btn.innerHTML = '详细参数 ▲';
            });
        }

        function collapseAll() {
            document.querySelectorAll('.details-content').forEach(content => {
                content.classList.remove('show');
            });
            document.querySelectorAll('.toggle-details').forEach(btn => {
                btn.innerHTML = '详细参数 ▼';
            });
        }
    </script>
</body>
</html>
