from manim import *
import numpy as np

class SimpleIntro(Scene):
    def construct(self):
        # 创建标题
        title = Text("量化投资动画演示", font_size=48, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        
        # 移动标题到顶部
        self.play(title.animate.to_edge(UP))
        
        # 创建一个简单的函数图
        axes = Axes(
            x_range=[-3, 3, 1],
            y_range=[-2, 2, 1],
            x_length=8,
            y_length=6,
        )
        
        # 指数函数
        exp_graph = axes.plot(lambda x: np.exp(x/2) - 1, color=YELLOW)
        exp_label = axes.get_graph_label(exp_graph, "e^{x/2} - 1", x_val=1, direction=UP)
        
        self.play(Create(axes))
        self.play(Create(exp_graph), Write(exp_label))
        self.wait(2)
        
        # 添加解释文本
        explanation = Text("这类似于复利增长曲线", font_size=32, color=GREEN)
        explanation.to_edge(DOWN)
        self.play(Write(explanation))
        self.wait(2)

class BasicShapes(Scene):
    def construct(self):
        # 创建基本形状
        circle = Circle(radius=1, color=RED)
        square = Square(side_length=2, color=BLUE)
        triangle = Triangle(color=GREEN)
        
        # 排列形状
        shapes = VGroup(circle, square, triangle)
        shapes.arrange(RIGHT, buff=1)
        
        # 动画显示
        self.play(Create(circle))
        self.play(Create(square))
        self.play(Create(triangle))
        self.wait(1)
        
        # 变换动画
        self.play(
            circle.animate.shift(UP * 2),
            square.animate.rotate(PI/4),
            triangle.animate.scale(1.5)
        )
        self.wait(2)

class TextAnimation(Scene):
    def construct(self):
        # 创建文本
        text1 = Text("QuantStats", font_size=60, color=BLUE)
        text2 = Text("量化分析工具", font_size=40, color=GREEN)
        
        # 显示第一个文本
        self.play(Write(text1))
        self.wait(1)
        
        # 移动并添加第二个文本
        self.play(text1.animate.shift(UP * 1.5))
        self.play(Write(text2))
        self.wait(1)
        
        # 创建列表
        features = [
            "• 计算期望收益率",
            "• 分析风险指标", 
            "• 评估投资表现",
            "• 生成报告图表"
        ]
        
        feature_group = VGroup()
        for i, feature in enumerate(features):
            feature_text = Text(feature, font_size=28, color=WHITE)
            feature_text.shift(DOWN * (i * 0.8 + 0.5))
            feature_group.add(feature_text)
        
        # 逐个显示特性
        for feature in feature_group:
            self.play(Write(feature), run_time=0.8)
            self.wait(0.3)
        
        self.wait(2)

class MathFormula(Scene):
    def construct(self):
        # 标题
        title = Text("数学公式动画", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        
        # 夏普比率公式
        sharpe_formula = MathTex(
            r"\text{Sharpe Ratio} = \frac{E[R] - R_f}{\sigma}",
            font_size=48
        )
        
        self.play(Write(sharpe_formula))
        self.wait(2)
        
        # 分解公式
        numerator = MathTex(r"E[R] - R_f", color=GREEN, font_size=36)
        numerator_desc = Text("超额收益", font_size=24, color=GREEN)
        
        denominator = MathTex(r"\sigma", color=RED, font_size=36)
        denominator_desc = Text("波动率", font_size=24, color=RED)
        
        # 排列解释
        explanation = VGroup(
            VGroup(numerator, numerator_desc).arrange(DOWN),
            VGroup(denominator, denominator_desc).arrange(DOWN)
        ).arrange(RIGHT, buff=2).shift(DOWN * 2)
        
        self.play(
            sharpe_formula.animate.shift(UP * 1.5),
            FadeIn(explanation)
        )
        self.wait(3)

# 运行说明
"""
要运行这些动画，请在命令行中使用以下命令：

1. 基本介绍动画：
   manim -pql simple_manim_demo.py SimpleIntro

2. 基本形状动画：
   manim -pql simple_manim_demo.py BasicShapes

3. 文本动画：
   manim -pql simple_manim_demo.py TextAnimation

4. 数学公式动画：
   manim -pql simple_manim_demo.py MathFormula

参数说明：
-p: 播放视频
-q: 质量设置 (l=低质量, m=中等质量, h=高质量)
-l: 低质量渲染（快速预览）

高质量渲染：
manim -pqh simple_manim_demo.py SimpleIntro
"""
