from manim import *
import numpy as np

class EMAIntroduction(Scene):
    def construct(self):
        # 主标题
        title = Text("EMA 指数移动平均线", font_size=60, color=BLUE, weight=BOLD)
        subtitle = Text("Exponential Moving Average", font_size=36, color=WHITE)
        
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.5)
        
        self.play(Write(title), run_time=2)
        self.play(Write(subtitle), run_time=1.5)
        self.wait(1)
        
        # 移动标题到顶部
        self.play(title_group.animate.to_edge(UP, buff=0.5))
        
        # EMA特点介绍
        features = [
            "📈 对近期价格赋予更高权重",
            "⚡ 反应速度比SMA更快",
            "🎯 减少滞后性",
            "📊 广泛用于技术分析",
            "🔄 递归计算方式"
        ]
        
        feature_objects = VGroup()
        for i, feature in enumerate(features):
            feature_text = Text(feature, font_size=32, color=YELLOW)
            feature_text.shift(DOWN * (i * 0.8 - 1.5))
            feature_objects.add(feature_text)
        
        # 逐个显示特性
        for feature in feature_objects:
            self.play(Write(feature), run_time=1)
            self.wait(0.5)
        
        self.wait(2)

class EMAFormulaExplanation(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 计算公式详解", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 基本公式
        basic_formula = MathTex(
            r"\text{EMA}_t = \alpha \cdot P_t + (1-\alpha) \cdot \text{EMA}_{t-1}",
            font_size=44,
            color=WHITE
        )
        
        self.play(Write(basic_formula))
        self.wait(2)
        
        # 移动公式到上方
        self.play(basic_formula.animate.shift(UP * 2))
        
        # 参数解释
        param_explanations = VGroup(
            MathTex(r"\text{EMA}_t", color=YELLOW, font_size=36),
            Text("= 当前期的EMA值", font_size=24, color=WHITE),
            
            MathTex(r"P_t", color=GREEN, font_size=36),
            Text("= 当前期的价格", font_size=24, color=WHITE),
            
            MathTex(r"\text{EMA}_{t-1}", color=RED, font_size=36),
            Text("= 前一期的EMA值", font_size=24, color=WHITE),
            
            MathTex(r"\alpha", color=PURPLE, font_size=36),
            Text("= 平滑系数 (0 < α < 1)", font_size=24, color=WHITE)
        ).arrange_in_grid(rows=4, cols=2, buff=(1, 0.5))
        
        param_explanations.shift(DOWN * 0.5)
        
        # 逐个显示参数解释
        for i in range(0, len(param_explanations), 2):
            self.play(
                Write(param_explanations[i]),
                Write(param_explanations[i+1]),
                run_time=1.5
            )
            self.wait(0.8)
        
        self.wait(2)

class AlphaCalculation(Scene):
    def construct(self):
        # 标题
        title = Text("平滑系数 α 的计算", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # α的计算公式
        alpha_formula = MathTex(
            r"\alpha = \frac{2}{N + 1}",
            font_size=48,
            color=YELLOW
        )
        
        self.play(Write(alpha_formula))
        self.wait(2)
        
        # 移动公式
        self.play(alpha_formula.animate.shift(UP * 1.5))
        
        # N的解释
        n_explanation = VGroup(
            Text("其中 N = EMA的周期数", font_size=32, color=WHITE),
            Text("例如：12日EMA, 26日EMA", font_size=28, color=GRAY)
        ).arrange(DOWN, buff=0.3)
        
        self.play(Write(n_explanation))
        self.wait(1)
        
        # 常见周期的α值
        examples_title = Text("常见周期的 α 值：", font_size=32, color=GREEN)
        examples_title.shift(DOWN * 1)
        
        examples = VGroup(
            MathTex(r"\text{EMA}_{12}: \alpha = \frac{2}{12+1} = 0.154", color=WHITE, font_size=28),
            MathTex(r"\text{EMA}_{26}: \alpha = \frac{2}{26+1} = 0.074", color=WHITE, font_size=28),
            MathTex(r"\text{EMA}_{50}: \alpha = \frac{2}{50+1} = 0.039", color=WHITE, font_size=28)
        ).arrange(DOWN, buff=0.4).shift(DOWN * 2.2)
        
        self.play(Write(examples_title))
        self.wait(0.5)
        
        for example in examples:
            self.play(Write(example), run_time=1)
            self.wait(0.5)
        
        self.wait(2)

class EMACalculationDemo(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 计算演示", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 示例数据
        prices = [100, 102, 98, 105, 103, 107, 104, 109]
        n_period = 5
        alpha = 2 / (n_period + 1)
        
        # 显示参数
        params = VGroup(
            Text(f"周期 N = {n_period}", font_size=28, color=WHITE),
            MathTex(f"\\alpha = \\frac{{2}}{{{n_period}+1}} = {alpha:.3f}", font_size=28, color=YELLOW),
            Text(f"价格序列: {prices}", font_size=24, color=GRAY)
        ).arrange(DOWN, buff=0.3).to_edge(LEFT, buff=1).shift(UP * 2)
        
        self.play(Write(params))
        self.wait(1)
        
        # 计算过程
        calculation_title = Text("计算过程：", font_size=32, color=GREEN)
        calculation_title.to_edge(LEFT, buff=1).shift(UP * 0.5)
        self.play(Write(calculation_title))
        
        # 初始EMA（使用SMA）
        initial_sma = sum(prices[:n_period]) / n_period
        initial_text = MathTex(
            f"\\text{{EMA}}_5 = \\text{{SMA}}_5 = {initial_sma:.2f}",
            font_size=24,
            color=WHITE
        ).to_edge(LEFT, buff=1).shift(UP * 0.1)
        
        self.play(Write(initial_text))
        self.wait(1)
        
        # 逐步计算后续EMA
        ema_values = [initial_sma]
        calculation_steps = VGroup()
        
        for i in range(n_period, len(prices)):
            current_price = prices[i]
            prev_ema = ema_values[-1]
            current_ema = alpha * current_price + (1 - alpha) * prev_ema
            ema_values.append(current_ema)
            
            step_formula = MathTex(
                f"\\text{{EMA}}_{{{i+1}}} = {alpha:.3f} \\times {current_price} + {1-alpha:.3f} \\times {prev_ema:.2f} = {current_ema:.2f}",
                font_size=20,
                color=YELLOW
            )
            step_formula.to_edge(LEFT, buff=1).shift(DOWN * (i - n_period + 1) * 0.4)
            calculation_steps.add(step_formula)
            
            self.play(Write(step_formula), run_time=1.5)
            self.wait(0.8)
        
        self.wait(2)

class EMAvsMAComparison(Scene):
    def construct(self):
        # 标题
        title = Text("EMA vs SMA 对比", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建坐标轴
        axes = Axes(
            x_range=[0, 20, 5],
            y_range=[95, 115, 5],
            x_length=10,
            y_length=6,
            axis_config={"color": WHITE},
            x_axis_config={"numbers_to_include": np.arange(0, 21, 5)},
            y_axis_config={"numbers_to_include": np.arange(95, 116, 5)},
        )
        
        x_label = axes.get_x_axis_label("时间")
        y_label = axes.get_y_axis_label("价格", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 模拟价格数据
        np.random.seed(42)
        n_points = 20
        base_prices = np.linspace(100, 110, n_points)
        noise = np.random.normal(0, 2, n_points)
        prices = base_prices + noise
        
        # 计算SMA和EMA
        period = 5
        alpha = 2 / (period + 1)
        
        # SMA计算
        sma_values = []
        for i in range(len(prices)):
            if i < period - 1:
                sma_values.append(None)
            else:
                sma_values.append(np.mean(prices[i-period+1:i+1]))
        
        # EMA计算
        ema_values = []
        for i in range(len(prices)):
            if i == 0:
                ema_values.append(prices[i])
            else:
                ema_values.append(alpha * prices[i] + (1 - alpha) * ema_values[i-1])
        
        # 绘制价格线
        price_points = [axes.coords_to_point(i, prices[i]) for i in range(n_points)]
        price_line = VMobject()
        price_line.set_points_as_corners(price_points)
        price_line.set_color(WHITE)
        
        # 绘制SMA线
        sma_points = [axes.coords_to_point(i, sma_values[i]) for i in range(period-1, n_points)]
        sma_line = VMobject()
        sma_line.set_points_as_corners(sma_points)
        sma_line.set_color(BLUE)
        
        # 绘制EMA线
        ema_points = [axes.coords_to_point(i, ema_values[i]) for i in range(n_points)]
        ema_line = VMobject()
        ema_line.set_points_as_corners(ema_points)
        ema_line.set_color(RED)
        
        # 图例
        legend = VGroup(
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=WHITE), Text("价格", font_size=20)),
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=BLUE), Text("SMA(5)", font_size=20)),
            VGroup(Line(ORIGIN, RIGHT * 0.5, color=RED), Text("EMA(5)", font_size=20))
        ).arrange(DOWN, aligned_edge=LEFT).to_corner(UR)
        
        # 动画绘制
        self.play(Create(price_line), run_time=2)
        self.play(Create(sma_line), run_time=2)
        self.play(Create(ema_line), run_time=2)
        self.play(Write(legend))
        
        # 对比说明
        comparison_text = VGroup(
            Text("EMA特点:", font_size=24, color=RED),
            Text("• 反应更快", font_size=20, color=WHITE),
            Text("• 滞后性更小", font_size=20, color=WHITE),
            Text("• 对近期价格更敏感", font_size=20, color=WHITE)
        ).arrange(DOWN, aligned_edge=LEFT).to_corner(DL)
        
        self.play(Write(comparison_text))
        self.wait(3)

class EMAWeightVisualization(Scene):
    def construct(self):
        # 标题
        title = Text("EMA 权重分布可视化", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 参数设置
        alpha = 0.2
        periods = 10
        
        # 计算权重
        weights = []
        for i in range(periods):
            if i == 0:
                weight = alpha
            else:
                weight = alpha * (1 - alpha) ** i
            weights.append(weight)
        
        # 创建柱状图
        axes = Axes(
            x_range=[0, periods, 1],
            y_range=[0, max(weights) * 1.2, 0.05],
            x_length=10,
            y_length=6,
            axis_config={"color": WHITE},
        )
        
        x_label = axes.get_x_axis_label("期数 (t-i)")
        y_label = axes.get_y_axis_label("权重", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 创建权重柱状图
        bars = VGroup()
        for i, weight in enumerate(weights):
            bar_height = weight * 100  # 缩放因子
            bar = Rectangle(
                width=0.8,
                height=bar_height,
                color=interpolate_color(RED, YELLOW, weight/max(weights)),
                fill_opacity=0.8
            )
            bar.move_to(axes.coords_to_point(i + 0.5, weight/2))
            bars.add(bar)
            
            # 添加权重标签
            weight_label = Text(f"{weight:.3f}", font_size=16, color=WHITE)
            weight_label.next_to(bar, UP, buff=0.1)
            bars.add(weight_label)
        
        # 动画显示柱状图
        for bar in bars:
            self.play(GrowFromEdge(bar, DOWN), run_time=0.3)
        
        # 添加说明
        explanation = VGroup(
            MathTex(f"\\alpha = {alpha}", font_size=28, color=YELLOW),
            Text("权重呈指数递减", font_size=24, color=WHITE),
            Text("近期数据权重更高", font_size=24, color=GREEN)
        ).arrange(DOWN, buff=0.3).to_corner(UR)
        
        self.play(Write(explanation))
        self.wait(3)

# 运行说明
"""
运行这些EMA动画的命令：

1. EMA介绍：
   manim -pql ema_formula_animation.py EMAIntroduction

2. EMA公式详解：
   manim -pql ema_formula_animation.py EMAFormulaExplanation

3. Alpha系数计算：
   manim -pql ema_formula_animation.py AlphaCalculation

4. EMA计算演示：
   manim -pql ema_formula_animation.py EMACalculationDemo

5. EMA vs SMA对比：
   manim -pql ema_formula_animation.py EMAvsMAComparison

6. EMA权重可视化：
   manim -pql ema_formula_animation.py EMAWeightVisualization

生成高质量视频：
manim -pqh ema_formula_animation.py EMAIntroduction

批量生成所有动画：
manim -pql ema_formula_animation.py EMAIntroduction EMAFormulaExplanation AlphaCalculation EMACalculationDemo EMAvsMAComparison EMAWeightVisualization
"""
